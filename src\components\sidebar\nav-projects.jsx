import React, { useState } from "react";
import {
  Folder,
  FolderOpen,
  FileText,
  Code,
  Database,
  Settings,
  Globe,
  Image,
  Music,
  Video,
  MoreHorizontal,
  SquarePen,
  Trash2,
  Copy,
  Plus,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { useProjects } from "@/contexts/ProjectContext";
import { ProjectDialog } from "@/components/projects/ProjectDialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

// Icon mapping
const iconMap = {
  Folder,
  FolderOpen,
  FileText,
  Code,
  Database,
  Settings,
  Globe,
  Image,
  Music,
  Video,
};

export function NavProjects() {
  const { isMobile } = useSidebar();
  const { projects, deleteProject, duplicateProject } = useProjects();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingProject, setEditingProject] = useState(null);
  const [dialogMode, setDialogMode] = useState("create");
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const handleCreateProject = () => {
    setEditingProject(null);
    setDialogMode("create");
    setDialogOpen(true);
  };

  const handleEditProject = (project) => {
    setEditingProject(project);
    setDialogMode("edit");
    setDialogOpen(true);
  };

  const handleDuplicateProject = async (project) => {
    try {
      await duplicateProject(project.id);
    } catch (error) {
      console.error("Failed to duplicate project:", error);
    }
  };

  const handleDeleteProject = (project) => {
    setProjectToDelete(project);
    setConfirmDialogOpen(true);
  };

  const confirmDeleteProject = async () => {
    if (!projectToDelete) return;

    setDeleteLoading(true);
    try {
      await deleteProject(projectToDelete.id);
      setConfirmDialogOpen(false);
      setProjectToDelete(null);
    } catch (error) {
      console.error("Failed to delete project:", error);
    } finally {
      setDeleteLoading(false);
    }
  };

  const getProjectIcon = (iconName) => {
    return iconMap[iconName] || Folder;
  };

  return (
    <>
      <SidebarGroup className="group-data-[collapsible=icon]:hidden">
        <div className="flex items-center justify-between">
          <SidebarGroupLabel>Projects</SidebarGroupLabel>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCreateProject}
            className="h-6 w-6 p-0"
          >
            <Plus className="h-4 w-4" />
            <span className="sr-only">Add Project</span>
          </Button>
        </div>
        <SidebarMenu>
          {projects.map((project) => {
            const IconComponent = getProjectIcon(project.icon);
            return (
              <SidebarMenuItem key={project.id}>
                <SidebarMenuButton asChild>
                  <a href={project.url} className="text-gray-700">
                    <IconComponent />
                    <span>{project.name}</span>
                  </a>
                </SidebarMenuButton>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <SidebarMenuAction showOnHover>
                      <MoreHorizontal />
                      <span className="sr-only">More</span>
                    </SidebarMenuAction>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="w-48 text-gray-700"
                    side={isMobile ? "bottom" : "right"}
                    align={isMobile ? "end" : "start"}
                  >
                    <DropdownMenuItem
                      onClick={() => window.open(project.url, "_blank")}
                    >
                      <Folder className="text-muted-foreground" />
                      <span>View Project</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleEditProject(project)}
                    >
                      <SquarePen className="text-muted-foreground" />
                      <span>Edit Project</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleDuplicateProject(project)}
                    >
                      <Copy className="text-muted-foreground" />
                      <span>Duplicate Project</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => handleDeleteProject(project)}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="text-red-600" />
                      <span>Delete Project</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarGroup>

      <ProjectDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        project={editingProject}
        mode={dialogMode}
      />

      <ConfirmDialog
        open={confirmDialogOpen}
        onOpenChange={setConfirmDialogOpen}
        title="Delete Project"
        description={`Are you sure you want to delete "${projectToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={confirmDeleteProject}
        loading={deleteLoading}
      />
    </>
  );
}
